const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuestNdaAgreement = sequelize.define(
    "AppointmentGuestNdaAgreement",
    {
      appointment_guest_nda_agreement_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_guest_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment_guest",
          key: "appointment_guest_id",
        },
        onDelete: "CASCADE",
      },
      nda_agreement_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_agreement",
          key: "nda_agreement_id",
        },
        onDelete: "CASCADE",
      }},
    {
      tableName: "appointment_guest_nda_agreement",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ["appointment_guest_id", "nda_agreement_id"],
        },
      ],
    }
  );

  AppointmentGuestNdaAgreement.associate = (models) => {
    AppointmentGuestNdaAgreement.belongsTo(models.AppointmentGuest, {
      foreignKey: "appointment_guest_id",
      as: "appointmentGuest",
    });

    AppointmentGuestNdaAgreement.belongsTo(models.NdaAgreement, {
      foreignKey: "nda_agreement_id",
      as: "ndaAgreement",
    });
  };

  history(AppointmentGuestNdaAgreement, sequelize, DataTypes);

  return AppointmentGuestNdaAgreement;
};
